USE tm;

CREATE VIEW fbt_kingdee_cn AS
SELECT 
    accounting_period AS '会计期间',
    material_category_1 AS '1级物料类别',
    material_category_2 AS '2级物料类别', 
    material_category_3 AS '3级物料类别',
    material_code AS '物料代码',
    material_name AS '物料名称',
    specification AS '规格型号',
    unit_of_measure AS '计量单位',
    opening_quantity AS '期初结存数量',
    opening_unit_price AS '期初结存单价',
    opening_amount AS '期初结存金额',
    current_receipt_quantity AS '本期收入数量',
    current_receipt_unit_price AS '本期收入单价',
    current_receipt_amount AS '本期收入金额',
    current_issue_quantity AS '本期发出数量',
    current_issue_unit_price AS '本期发出单价',
    current_issue_amount AS '本期发出金额',
    closing_quantity AS '期末结存数量',
    closing_unit_price AS '期末结存单价',
    closing_amount AS '期末结存金额'
FROM fbt_kingdee;
