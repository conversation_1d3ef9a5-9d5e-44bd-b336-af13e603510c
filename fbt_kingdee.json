["ibd2sdi", {"type": 1, "id": 983, "object": {"mysqld_version_id": 80033, "dd_version": 80023, "sdi_version": 80019, "dd_object_type": "Table", "dd_object": {"name": "fbt_kingdee", "mysql_version_id": 80033, "created": 20240407025534, "last_altered": 20240407025534, "hidden": 1, "options": "avg_row_length=0;encrypt_type=N;key_block_size=0;keys_disabled=0;pack_record=1;stats_auto_recalc=0;stats_sample_pages=0;", "columns": [{"name": "会计期间", "type": 15, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 1, "char_length": 10, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "date", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "1级物料类别", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 2, "char_length": 200, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(50)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "2级物料类别", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 3, "char_length": 200, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(50)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "3级物料类别", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 4, "char_length": 1020, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(255)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "物料代码", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 5, "char_length": 200, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(50)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "物料名称", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 6, "char_length": 200, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(50)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "规格型号", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 7, "char_length": 200, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(50)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "计量单位", "type": 16, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 8, "char_length": 200, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "<PERSON><PERSON><PERSON>(50)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "期初结存数量", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 9, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "期初结存单价", "type": 21, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 10, "char_length": 18, "numeric_precision": 16, "numeric_scale": 8, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "decimal(16,8)", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "期初结存金额", "type": 21, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 11, "char_length": 18, "numeric_precision": 16, "numeric_scale": 2, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "decimal(16,2)", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "本期收入数量", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 12, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "本期收入单价", "type": 21, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 13, "char_length": 18, "numeric_precision": 16, "numeric_scale": 8, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "decimal(16,8)", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "本期收入金额", "type": 21, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 14, "char_length": 12, "numeric_precision": 10, "numeric_scale": 2, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "decimal(10,2)", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "本期发出数量", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 15, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "本期发出单价", "type": 21, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 16, "char_length": 18, "numeric_precision": 16, "numeric_scale": 8, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "decimal(16,8)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "本期发出金额", "type": 21, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 17, "char_length": 12, "numeric_precision": 10, "numeric_scale": 2, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "decimal(10,2)", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "期末结存数量", "type": 4, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 18, "char_length": 11, "numeric_precision": 10, "numeric_scale": 0, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "int", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "期末结存单价", "type": 21, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 19, "char_length": 18, "numeric_precision": 16, "numeric_scale": 8, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "decimal(16,8)", "elements": [], "collation_id": 255, "is_explicit_collation": false}, {"name": "期末结存金额", "type": 21, "is_nullable": true, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 1, "ordinal_position": 20, "char_length": 12, "numeric_precision": 10, "numeric_scale": 2, "numeric_scale_null": false, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "interval_count=0;", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "decimal(10,2)", "elements": [], "collation_id": 8, "is_explicit_collation": false}, {"name": "DB_ROW_ID", "type": 10, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 2, "ordinal_position": 21, "char_length": 6, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "", "elements": [], "collation_id": 63, "is_explicit_collation": false}, {"name": "DB_TRX_ID", "type": 10, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 2, "ordinal_position": 22, "char_length": 6, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "", "elements": [], "collation_id": 63, "is_explicit_collation": false}, {"name": "DB_ROLL_PTR", "type": 9, "is_nullable": false, "is_zerofill": false, "is_unsigned": false, "is_auto_increment": false, "is_virtual": false, "hidden": 2, "ordinal_position": 23, "char_length": 7, "numeric_precision": 0, "numeric_scale": 0, "numeric_scale_null": true, "datetime_precision": 0, "datetime_precision_null": 1, "has_no_default": false, "default_value_null": true, "srs_id_null": true, "srs_id": 0, "default_value": "", "default_value_utf8_null": true, "default_value_utf8": "", "default_option": "", "update_option": "", "comment": "", "generation_expression": "", "generation_expression_utf8": "", "options": "", "se_private_data": "table_id=1489;", "engine_attribute": "", "secondary_engine_attribute": "", "column_key": 1, "column_type_utf8": "", "elements": [], "collation_id": 63, "is_explicit_collation": false}], "schema_ref": "tm", "se_private_id": 1489, "engine": "InnoDB", "last_checked_for_upgrade_version_id": 0, "comment": "", "se_private_data": "", "engine_attribute": "", "secondary_engine_attribute": "", "row_format": 2, "partition_type": 0, "partition_expression": "", "partition_expression_utf8": "", "default_partitioning": 0, "subpartition_type": 0, "subpartition_expression": "", "subpartition_expression_utf8": "", "default_subpartitioning": 0, "indexes": [{"name": "PRIMARY", "hidden": true, "is_generated": false, "ordinal_position": 1, "comment": "", "options": "", "se_private_data": "id=617;root=4;space_id=427;table_id=1489;trx_id=255534;", "type": 2, "algorithm": 2, "is_algorithm_explicit": false, "is_visible": true, "engine": "InnoDB", "engine_attribute": "", "secondary_engine_attribute": "", "elements": [{"ordinal_position": 1, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 20}, {"ordinal_position": 2, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 21}, {"ordinal_position": 3, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 22}, {"ordinal_position": 4, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 0}, {"ordinal_position": 5, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 1}, {"ordinal_position": 6, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 2}, {"ordinal_position": 7, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 3}, {"ordinal_position": 8, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 4}, {"ordinal_position": 9, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 5}, {"ordinal_position": 10, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 6}, {"ordinal_position": 11, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 7}, {"ordinal_position": 12, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 8}, {"ordinal_position": 13, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 9}, {"ordinal_position": 14, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 10}, {"ordinal_position": 15, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 11}, {"ordinal_position": 16, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 12}, {"ordinal_position": 17, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 13}, {"ordinal_position": 18, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 14}, {"ordinal_position": 19, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 15}, {"ordinal_position": 20, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 16}, {"ordinal_position": 21, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 17}, {"ordinal_position": 22, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 18}, {"ordinal_position": 23, "length": 4294967295, "order": 2, "hidden": true, "column_opx": 19}], "tablespace_ref": "tm/fbt_kingdee"}], "foreign_keys": [], "check_constraints": [], "partitions": [], "collation_id": 255}}}, {"type": 2, "id": 432, "object": {"mysqld_version_id": 80033, "dd_version": 80023, "sdi_version": 80019, "dd_object_type": "Tablespace", "dd_object": {"name": "tm/fbt_kingdee", "comment": "", "options": "autoextend_size=0;encryption=N;", "se_private_data": "flags=16417;id=427;server_version=80033;space_version=1;state=normal;", "engine": "InnoDB", "engine_attribute": "", "files": [{"ordinal_position": 1, "filename": ".\\tm\\fbt_kingdee.ibd", "se_private_data": "id=427;"}]}}}]