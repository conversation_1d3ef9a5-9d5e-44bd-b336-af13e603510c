USE tm;

CREATE TABLE fbt_kingdee (
    accounting_period DATE,
    material_category_1 VARCHAR(50),
    material_category_2 VARCHAR(50),
    material_category_3 VARCHAR(255),
    material_code VARCHAR(50),
    material_name VARCHAR(50),
    specification VARCHAR(50),
    unit_of_measure VARCHAR(50),
    opening_quantity INT,
    opening_unit_price DECIMAL(16,8),
    opening_amount DECIMAL(16,2),
    current_receipt_quantity INT,
    current_receipt_unit_price DECIMAL(16,8),
    current_receipt_amount DECIMAL(10,2),
    current_issue_quantity INT,
    current_issue_unit_price DECIMAL(16,8),
    current_issue_amount DECIMAL(10,2),
    closing_quantity INT,
    closing_unit_price DECIMAL(16,8),
    closing_amount DECIMAL(10,2)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
